<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Setting;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 删除Setting表中的微信相关配置项
        $wechatSettings = [
            'wechat_app_id',
            'wechat_secret', 
            'wechat_token',
            'wechat_aes_key'
        ];
        
        foreach ($wechatSettings as $key) {
            Setting::where('key', $key)->delete();
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // 恢复微信配置项（从环境变量）
        $wechatSettings = [
            'wechat_app_id' => env('WECHAT_APP_ID', ''),
            'wechat_secret' => env('WECHAT_SECRET', ''),
            'wechat_token' => env('WECHAT_TOKEN', ''),
            'wechat_aes_key' => env('WECHAT_AES_KEY', '')
        ];
        
        foreach ($wechatSettings as $key => $value) {
            if ($value) {
                Setting::updateOrCreate(
                    ['key' => $key],
                    ['value' => $value]
                );
            }
        }
    }
};
