<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('wechat_messages', function (Blueprint $table) {
            $table->id();
            $table->foreignId('wechat_account_id')->constrained()->onDelete('cascade');
            $table->string('openid', 64)->comment('用户openid');
            $table->string('msg_id', 64)->nullable()->comment('消息ID');
            $table->string('msg_type', 32)->comment('消息类型');
            $table->text('content')->nullable()->comment('消息内容');
            $table->string('media_id', 128)->nullable()->comment('媒体文件ID');
            $table->string('pic_url', 512)->nullable()->comment('图片链接');
            $table->string('format', 16)->nullable()->comment('语音格式');
            $table->text('recognition')->nullable()->comment('语音识别结果');
            $table->string('thumb_media_id', 128)->nullable()->comment('视频缩略图媒体ID');
            $table->decimal('location_x', 10, 6)->nullable()->comment('地理位置纬度');
            $table->decimal('location_y', 10, 6)->nullable()->comment('地理位置经度');
            $table->integer('scale')->nullable()->comment('地图缩放大小');
            $table->string('label', 256)->nullable()->comment('地理位置信息');
            $table->string('title', 256)->nullable()->comment('消息标题');
            $table->text('description')->nullable()->comment('消息描述');
            $table->string('url', 512)->nullable()->comment('消息链接');
            $table->string('event', 32)->nullable()->comment('事件类型');
            $table->string('event_key', 128)->nullable()->comment('事件KEY值');
            $table->string('ticket', 128)->nullable()->comment('二维码ticket');
            $table->decimal('latitude', 10, 6)->nullable()->comment('纬度');
            $table->decimal('longitude', 10, 6)->nullable()->comment('经度');
            $table->decimal('precision', 8, 2)->nullable()->comment('位置精度');
            $table->enum('status', ['pending', 'processed', 'failed'])->default('pending')->comment('处理状态');
            $table->text('reply_content')->nullable()->comment('回复内容');
            $table->string('reply_type', 32)->nullable()->comment('回复类型');
            $table->string('reply_media_id', 128)->nullable()->comment('回复媒体ID');
            $table->timestamp('replied_at')->nullable()->comment('回复时间');
            $table->timestamp('created_time')->nullable()->comment('消息创建时间');
            $table->timestamps();
            $table->softDeletes();
            
            $table->index(['wechat_account_id', 'openid']);
            $table->index(['wechat_account_id', 'msg_type']);
            $table->index(['wechat_account_id', 'status']);
            $table->index(['wechat_account_id', 'event']);
            $table->index('created_time');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('wechat_messages');
    }
};