<?php

namespace App\Filament\Resources;

use App\Filament\Resources\PostResource\Pages;
use App\Models\Post;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\Str;
use Rawilk\FilamentQuill\Filament\Forms\Components\QuillEditor;
use Filament\Forms\Components\RichEditor;
use CodeWithDennis\FilamentSelectTree\SelectTree;
use Filament\Forms\Components\View;
use Kahusoftware\FilamentCkeditorField\CKEditor;
class PostResource extends Resource
{

    protected static ?string $model = Post::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static ?string $navigationGroup = '内容管理';

    protected static ?int $navigationSort = 2;

    protected static ?int $navigationGroupSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('基本信息')
                    ->schema([
                        Forms\Components\TextInput::make('title')
                            ->label('标题')
                            ->required()
                            ->maxLength(255)
                            ->reactive()
                            ->afterStateUpdated(function ($state, callable $set) {
                                $slug = strtolower(preg_replace('/[^a-zA-Z0-9]/', '', substr(pinyin_abbr($state), 0, 8)));
                                $set('slug', $slug);
                            }),
                        Forms\Components\TextInput::make('short_title')
                            ->label('简略标题')
                            ->maxLength(255),
                        Forms\Components\TextInput::make('slug')
                            ->label('别名')
                            ->required()
                            ->maxLength(255)
                            ->unique(ignoreRecord: true),
                        SelectTree::make('category_id')
                            ->label('分类')
                            ->relationship('category', 'name', 'parent_id')
                            ->searchable()
                            ->placeholder('请选择栏目')
                            ->required(),
                        SelectTree::make('subCategories')
                            ->label('副栏目')
                            ->relationship('subCategories', 'name', 'parent_id')
                            ->searchable()
                            ->multiple()
                            ->placeholder('选择副栏目'),
                        Forms\Components\Select::make('tags')
                            ->label('标签')
                            ->relationship('tags', 'name')
                            ->multiple()
                            ->preload()
                            ->searchable()
                            ->createOptionForm([
                                Forms\Components\TextInput::make('name')
                                    ->label('标签名称')
                                    ->required()
                                    ->maxLength(255),
                                Forms\Components\ColorPicker::make('color')
                                    ->label('标签颜色')
                                    ->default('#6B7280'),
                            ]),
                        Forms\Components\CheckboxList::make('custom_flags')
                            ->label('自定义属性')
                            ->options([
                                'h' => '头条',
                                'c' => '推荐',
                                'f' => '幻灯',
                                'a' => '特荐',
                                's' => '滚动',
                                'b' => '加粗',
                                'p' => '图片',
                                'j' => '跳转',
                            ])
                            ->columns(4),
                        Forms\Components\TextInput::make('author')
                            ->label('作者')
                            ->maxLength(255),
                        Forms\Components\TextInput::make('source')
                            ->label('文章来源')
                            ->maxLength(255),
                        Forms\Components\ColorPicker::make('title_color')
                            ->label('标题颜色'),
                        Forms\Components\FileUpload::make('thumb_image')
                            ->label('缩略图')
                            ->image()
                            ->directory('thumbnails')
                            ->disk('public')
                            ->imageEditor()
                            ->imageCropAspectRatio('1:1')
                            ->imageResizeMode('cover')
                            ->imageResizeTargetWidth(200)
                            ->imageResizeTargetHeight(200)
                            ->previewable(true)
                            ->downloadable(),
                        Forms\Components\Textarea::make('excerpt')
                            ->label('摘要')
                            ->maxLength(65535)
                            ->columnSpanFull(),
                    ])->columns(2),

                Forms\Components\Section::make('内容')
                    ->schema([
                        CKEditor::make('content')
                            ->label('内容')
                            ->required()
                            ->columnSpanFull()
                            ->uploadUrl('/admin/ckeditor/upload'),
                    ]),

                Forms\Components\Section::make('发布设置')
                    ->schema([
                        Forms\Components\Toggle::make('is_published')
                            ->label('是否发布')
                            ->default(false),
                        Forms\Components\DateTimePicker::make('published_at')
                            ->label('发布时间')
                            ->nullable(),
                        Forms\Components\Toggle::make('allow_comment')
                            ->label('允许评论')
                            ->default(true),
                        Forms\Components\Select::make('view_permission')
                            ->label('阅读权限')
                            ->options([
                                'public' => '开放浏览',
                                'pending' => '待审核稿件',
                                'admin' => 'admin',
                                'super-admin' => 'super-admin',
                                'system-auditor' => 'system-auditor',
                                'auditor' => 'auditor',
                                'user' => 'user',
                            ])
                            ->default('public'),
                        Forms\Components\TextInput::make('weight')
                            ->label('权重')
                            ->numeric()
                            ->default(0),
                        Forms\Components\TextInput::make('sort_order')
                            ->label('排序')
                            ->numeric()
                            ->default(0),
                        Forms\Components\CheckboxList::make('extra_options')
                            ->label('附加选项')
                            ->options([
                                'download_remote' => '下载远程图片和资源',
                                'first_image_thumb' => '提取第一个图片为缩略图',
                            ]),
                    ])->columns(3),

                Forms\Components\Section::make('统计信息')
                    ->schema([
                        Forms\Components\TextInput::make('view_count')
                            ->numeric()
                            ->disabled()
                            ->label('阅读量'),
                        Forms\Components\TextInput::make('comment_count')
                            ->numeric()
                            ->disabled()
                            ->label('评论数'),
                        Forms\Components\TextInput::make('like_count')
                            ->numeric()
                            ->disabled()
                            ->label('点赞数'),
                        Forms\Components\TextInput::make('hot_score')
                            ->numeric()
                            ->disabled()
                            ->label('热度分数'),
                        Forms\Components\DateTimePicker::make('last_viewed_at')
                            ->disabled()
                            ->label('最后查看时间'),
                        Forms\Components\DateTimePicker::make('last_commented_at')
                            ->disabled()
                            ->label('最后评论时间'),
                    ])
                    ->columns(2)
                    ->collapsible(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->label('标题')
                    ->searchable(),
                Tables\Columns\TextColumn::make('short_title')
                    ->label('简略标题'),
                Tables\Columns\TextColumn::make('category.name')
                    ->label('主栏目'),
                Tables\Columns\TextColumn::make('subCategories.name')
                    ->label('副栏目')
                    ->badge()
                    ->limit(2),
                Tables\Columns\TextColumn::make('tags.name')
                    ->label('标签')
                    ->badge(),
                Tables\Columns\TextColumn::make('weight')
                    ->label('权重')
                    ->sortable(),
                Tables\Columns\ImageColumn::make('thumb_image')
                    ->label('缩略图')
                    ->disk('public')
                    ->circular()
                    ->height(40)
                    ->width(40)
                    ->defaultImageUrl(url('/images/default-link.svg'))
                    ->extraImgAttributes(['style' => 'object-fit: cover;']),
                Tables\Columns\TextColumn::make('author')
                    ->label('作者'),
                Tables\Columns\TextColumn::make('source')
                    ->label('文章来源'),
                Tables\Columns\IconColumn::make('allow_comment')
                    ->label('允许评论')
                    ->boolean(),
                Tables\Columns\TextColumn::make('view_permission')
                    ->label('阅读权限'),
                Tables\Columns\IconColumn::make('is_published')
                    ->label('状态')
                    ->boolean(),
                Tables\Columns\TextColumn::make('published_at')
                    ->label('发布时间')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('sort_order')
                    ->label('排序')
                    ->sortable(),
                Tables\Columns\TextColumn::make('view_count')
                    ->numeric()
                    ->sortable()
                    ->label('阅读量'),
                Tables\Columns\TextColumn::make('comment_count')
                    ->numeric()
                    ->sortable()
                    ->label('评论数'),
                Tables\Columns\TextColumn::make('like_count')
                    ->numeric()
                    ->sortable()
                    ->label('点赞数'),
                Tables\Columns\TextColumn::make('hot_score')
                    ->numeric()
                    ->sortable()
                    ->label('热度分数'),
                Tables\Columns\TextColumn::make('last_viewed_at')
                    ->dateTime()
                    ->sortable()
                    ->label('最后查看时间'),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('创建时间')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label('更新时间')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('category')
                    ->label('分类')
                    ->relationship('category', 'name'),
                Tables\Filters\SelectFilter::make('tags')
                    ->label('标签')
                    ->relationship('tags', 'name')
                    ->multiple(),
                Tables\Filters\TernaryFilter::make('is_published')
                    ->label('状态')
                    ->boolean()
                    ->trueLabel('已发布')
                    ->falseLabel('未发布')
                    ->placeholder('全部'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            // \App\Filament\Resources\ArticleResource\RelationManagers\VersionsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPosts::route('/'),
            'create' => Pages\CreatePost::route('/create'),
            'edit' => Pages\EditPost::route('/{record}/edit'),
        ];
    }

    public static function getNavigationLabel(): string
    {
        return '文章管理';
    }

    public static function getModelLabel(): string
    {
        return '文章';
    }

    public static function getPluralModelLabel(): string
    {
        return '文章';
    }
}

// ====== 辅助函数移到类外部 ======

// 中文转拼音首字母（需安装 overtrue/pinyin）
if (!function_exists('pinyin_abbr')) {
    function pinyin_abbr($string) {
        try {
            return \Overtrue\Pinyin\Pinyin::abbr($string);
        } catch (\Throwable $e) {
            return substr($string, 0, 8);
        }
    }
}

// 递归生成带缩进的 options
if (!function_exists('buildCategoryTreeOptions')) {
    function buildCategoryTreeOptions($categories, $parentId = null, $prefix = '') {
        $options = [];
        foreach ($categories->where('parent_id', $parentId) as $cat) {
            $options[$cat->id] = $prefix . $cat->name;
            $options += buildCategoryTreeOptions($categories, $cat->id, $prefix . '——');
        }
        return $options;
    }
}