<?php

namespace App\Filament\Resources\AdministrativeInstitutionResource\Pages;

use App\Filament\Resources\AdministrativeInstitutionResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use SolutionForest\FilamentTree\Components\Tree;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;

class TreeAdministrativeInstitutions extends ListRecords
{
    protected static string $resource = AdministrativeInstitutionResource::class;

    public ?string $selectedLevel = null;

    public function mount(): void
    {
        parent::mount();
        // 从URL参数中获取筛选级别
        $this->selectedLevel = request()->get('level');
    }

    public function setLevel(?string $level)
    {
        $this->selectedLevel = $level;
        // 使用redirect来刷新页面并保持状态
        $url = static::getResource()::getUrl('tree');
        if ($level) {
            $url .= '?level=' . $level;
        }
        return redirect($url);
    }

    // 重写getTableQuery方法以确保筛选生效
    protected function getTableQuery(): Builder
    {
        $query = static::getResource()::getEloquentQuery();

        if ($this->selectedLevel) {
            switch ($this->selectedLevel) {
                case 'province':
                    // 只显示省级机构（顶级）
                    $query->where('administrative_level', 'province')
                          ->whereNull('parent_id');
                    break;
                case 'city':
                    // 显示省级机构和它们的直接子级（市级）
                    $query->where(function ($q) {
                        $q->where('administrative_level', 'province')
                          ->whereNull('parent_id');
                    })->orWhere(function ($q) {
                        $q->where('administrative_level', 'city')
                          ->whereHas('parent', function ($parent) {
                              $parent->where('administrative_level', 'province');
                          });
                    });
                    break;
                case 'district':
                    // 显示到区县级的完整层级
                    $query->where(function ($q) {
                        // 省级（顶级）
                        $q->where('administrative_level', 'province')
                          ->whereNull('parent_id');
                    })->orWhere(function ($q) {
                        // 市级（省级的子级）
                        $q->where('administrative_level', 'city')
                          ->whereHas('parent', function ($parent) {
                              $parent->where('administrative_level', 'province');
                          });
                    })->orWhere(function ($q) {
                        // 区县级（市级的子级）
                        $q->where('administrative_level', 'district')
                          ->whereHas('parent', function ($parent) {
                              $parent->where('administrative_level', 'city');
                          });
                    });
                    break;
                case 'town':
                    // 显示完整的四级层级结构
                    $query->where(function ($q) {
                        // 省级（顶级）
                        $q->where('administrative_level', 'province')
                          ->whereNull('parent_id');
                    })->orWhere(function ($q) {
                        // 市级（省级的子级）
                        $q->where('administrative_level', 'city')
                          ->whereHas('parent', function ($parent) {
                              $parent->where('administrative_level', 'province');
                          });
                    })->orWhere(function ($q) {
                        // 区县级（市级的子级）
                        $q->where('administrative_level', 'district')
                          ->whereHas('parent', function ($parent) {
                              $parent->where('administrative_level', 'city');
                          });
                    })->orWhere(function ($q) {
                        // 乡镇级（区县级的子级）
                        $q->where('administrative_level', 'town')
                          ->whereHas('parent', function ($parent) {
                              $parent->where('administrative_level', 'district');
                          });
                    });
                    break;
            }
        }

        return $query->with(['parent', 'children'])->orderBy('sort_order')->orderBy('name');
    }

    public function tree(): Tree
    {
        // 添加一个唯一的key来强制重新渲染Tree组件
        $treeKey = 'tree-' . ($this->selectedLevel ?? 'all') . '-' . time();

        return Tree::make($this)
            ->key($treeKey)
            ->primaryColumn('name')
            ->parentColumn('parent_id')
            ->enableSorting()
            ->sortColumn('sort_order')
            ->enableDragAndDrop()
            ->query(fn () => $this->getTableQuery())
            ->columns([
                TextColumn::make('name')
                    ->label('单位名称')
                    ->searchable()
                    ->limit(50),
                // 机构代码列已移除：字段未被实际使用
                TextColumn::make('full_region_name')
                    ->label('地区')
                    ->getStateUsing(function ($record) {
                        // 确保地区信息正确显示
                        $parts = array_filter([
                            $record->province_name,
                            $record->city_name,
                            $record->district_name,
                            $record->town_name
                        ]);
                        return implode(' - ', $parts) ?: '未设置';
                    })
                    ->limit(40)
                    ->toggleable(),
                BadgeColumn::make('administrative_level')
                    ->label('行政级别')
                    ->formatStateUsing(fn ($state) => match($state) {
                        'province' => '省级',
                        'city' => '市级',
                        'district' => '区县级',
                        'town' => '乡镇级',
                        default => '未知',
                    })
                    ->colors([
                        'danger' => 'province',
                        'warning' => 'city',
                        'success' => 'district',
                        'primary' => 'town',
                    ]),
                TextColumn::make('host_unit')
                    ->label('主办单位')
                    ->limit(30)
                    ->toggleable(),
                BadgeColumn::make('binding_status')
                    ->label('绑定状态')
                    ->formatStateUsing(fn ($state) => match($state) {
                        'bound' => '已绑定',
                        'unbound' => '未绑定',
                        default => '未知',
                    })
                    ->colors([
                        'success' => 'bound',
                        'danger' => 'unbound',
                    ])
                    ->toggleable(),
                IconColumn::make('is_active')
                    ->label('状态')
                    ->boolean()
                    ->toggleable(),
            ])
            ->actions([
                \Filament\Tables\Actions\ViewAction::make()
                    ->label('查看'),
                \Filament\Tables\Actions\EditAction::make()
                    ->label('编辑'),
                \Filament\Tables\Actions\DeleteAction::make()
                    ->label('删除'),
            ]);
    }

    protected function getHeaderActions(): array
    {
        return [
             Actions\Action::make('filter_province')
                 ->label('省级')
                 ->icon('heroicon-o-building-office-2')
                 ->color($this->selectedLevel === 'province' ? 'primary' : 'gray')
                 ->outlined($this->selectedLevel !== 'province')
                 ->url(static::getResource()::getUrl('tree') . '?level=province')
                 ->tooltip('只显示省级机构（包括省份和直辖市）'),
             Actions\Action::make('filter_city')
                 ->label('市级')
                 ->icon('heroicon-o-building-office')
                 ->color($this->selectedLevel === 'city' ? 'primary' : 'gray')
                 ->outlined($this->selectedLevel !== 'city')
                 ->url(static::getResource()::getUrl('tree') . '?level=city')
                 ->tooltip('显示省级和市级机构（包括直辖市的区）'),
             Actions\Action::make('filter_district')
                 ->label('区县级')
                 ->icon('heroicon-o-map')
                 ->color($this->selectedLevel === 'district' ? 'primary' : 'gray')
                 ->outlined($this->selectedLevel !== 'district')
                 ->url(static::getResource()::getUrl('tree') . '?level=district')
                 ->tooltip('显示省级、市级和区县级机构'),
             Actions\Action::make('filter_town')
                 ->label('乡镇级')
                 ->icon('heroicon-o-home')
                 ->color($this->selectedLevel === 'town' ? 'primary' : 'gray')
                 ->outlined($this->selectedLevel !== 'town')
                 ->url(static::getResource()::getUrl('tree') . '?level=town')
                 ->tooltip('显示所有级别机构（完整层级结构）'),
             Actions\Action::make('clear_filter')
                 ->label('显示全部')
                 ->icon('heroicon-o-x-mark')
                 ->color($this->selectedLevel === null ? 'primary' : 'gray')
                 ->outlined($this->selectedLevel !== null)
                 ->url(static::getResource()::getUrl('tree'))
                 ->tooltip('清除筛选，显示所有机构'),
             Actions\Action::make('check_data')
                 ->label('数据检查')
                 ->icon('heroicon-o-magnifying-glass')
                 ->color('warning')
                 ->action(function () {
                     $this->checkDataIntegrity();
                 })
                 ->tooltip('检查数据层级关系完整性')
                 ->visible(app()->environment('local')),
             Actions\Action::make('list_view')
                 ->label('列表视图')
                 ->icon('heroicon-o-list-bullet')
                 ->color('info')
                 ->url(static::getResource()::getUrl('index'))
                 ->tooltip('切换到列表视图管理'),
             Actions\CreateAction::make()
                 ->label('新增行政机构'),
        ];
    }

    public function getTitle(): string
    {
        $levelText = $this->getLevelText();
        return '行政机构树形管理' . ($levelText ? " - {$levelText}" : '');
    }

    public function getHeading(): string
    {
        $levelText = $this->getLevelText();
        return '行政机构树形管理' . ($levelText ? " - {$levelText}" : '');
    }

    public function getSubheading(): ?string
    {
        $baseText = '通过树形结构管理行政机构的层级关系，支持拖拽排序';
        $levelText = $this->getLevelText();
        $helpText = '💡 级别说明：省级（省份+直辖市）→ 市级（地级市+直辖市的区）→ 区县级（区县+街道）→ 乡镇级';

        // 添加调试信息（仅在开发环境显示）
        $debugInfo = '';
        if (app()->environment('local') && $this->selectedLevel) {
            $count = $this->getTableQuery()->count();
            $debugInfo = "\n🔍 调试：当前筛选级别 [{$this->selectedLevel}] 共找到 {$count} 条记录";
        }

        return $baseText . ($levelText ? "，当前筛选：{$levelText}" : '') . "\n" . $helpText . $debugInfo;
    }

    private function getLevelText(): ?string
    {
        return match($this->selectedLevel) {
            'province' => '省级机构（省份和直辖市）',
            'city' => '省级和市级机构（包括直辖市的区）',
            'district' => '省级、市级和区县级机构',
            'town' => '所有级别机构（完整层级结构）',
            default => null,
        };
    }

    /**
     * 检查数据完整性
     */
    public function checkDataIntegrity()
    {
        $issues = [];

        // 检查各级别的数量
        $provinceCounts = static::getResource()::getEloquentQuery()->where('administrative_level', 'province')->count();
        $cityCounts = static::getResource()::getEloquentQuery()->where('administrative_level', 'city')->count();
        $districtCounts = static::getResource()::getEloquentQuery()->where('administrative_level', 'district')->count();
        $townCounts = static::getResource()::getEloquentQuery()->where('administrative_level', 'town')->count();

        // 检查孤儿节点（有parent_id但找不到父级的记录）
        $orphans = static::getResource()::getEloquentQuery()
            ->whereNotNull('parent_id')
            ->whereDoesntHave('parent')
            ->count();

        // 检查层级关系错误
        $wrongLevelRelations = static::getResource()::getEloquentQuery()
            ->whereNotNull('parent_id')
            ->whereHas('parent', function ($query) {
                $query->where(function ($q) {
                    // 市级的父级不是省级
                    $q->where('administrative_institutions.administrative_level', 'city')
                      ->whereHas('parent', function ($parent) {
                          $parent->where('administrative_level', '!=', 'province');
                      });
                })->orWhere(function ($q) {
                    // 区县级的父级不是市级
                    $q->where('administrative_institutions.administrative_level', 'district')
                      ->whereHas('parent', function ($parent) {
                          $parent->where('administrative_level', '!=', 'city');
                      });
                })->orWhere(function ($q) {
                    // 乡镇级的父级不是区县级
                    $q->where('administrative_institutions.administrative_level', 'town')
                      ->whereHas('parent', function ($parent) {
                          $parent->where('administrative_level', '!=', 'district');
                      });
                });
            })
            ->count();

        $message = "数据检查结果：\n";
        $message .= "省级机构：{$provinceCounts} 个\n";
        $message .= "市级机构：{$cityCounts} 个\n";
        $message .= "区县级机构：{$districtCounts} 个\n";
        $message .= "乡镇级机构：{$townCounts} 个\n";
        $message .= "孤儿节点：{$orphans} 个\n";
        $message .= "层级关系错误：{$wrongLevelRelations} 个";

        \Filament\Notifications\Notification::make()
            ->title('数据完整性检查')
            ->body($message)
            ->info()
            ->persistent()
            ->send();
    }
}