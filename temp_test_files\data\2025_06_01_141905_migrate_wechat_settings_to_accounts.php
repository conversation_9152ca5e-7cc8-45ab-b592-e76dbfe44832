<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Setting;
use App\Models\WechatAccount;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 从Setting表迁移微信配置到WechatAccount表
        $wechatAppId = Setting::getValue('wechat_app_id');
        $wechatSecret = Setting::getValue('wechat_secret');
        $wechatToken = Setting::getValue('wechat_token');
        $wechatAesKey = Setting::getValue('wechat_aes_key');
        
        // 如果存在微信配置，则创建默认的微信账号
        if ($wechatAppId || $wechatSecret) {
            $existingAccount = WechatAccount::where('app_id', $wechatAppId)->first();
            
            if (!$existingAccount && ($wechatAppId || $wechatSecret)) {
                WechatAccount::create([
                    'name' => '默认公众号',
                    'app_id' => $wechatAppId ?: '',
                    'app_secret' => $wechatSecret ?: '',
                    'account_type' => 'subscription',
                    'description' => '从旧配置迁移的默认公众号',
                    'is_active' => true,
                    'auto_collect' => false,
                    'collect_interval' => 60,
                    'collect_config' => json_encode([
                        'token' => $wechatToken ?: '',
                        'encoding_aes_key' => $wechatAesKey ?: '',
                        'max_articles_per_collect' => 20,
                        'collect_images' => true,
                        'convert_to_article' => false
                    ])
                ]);
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // 删除迁移创建的默认账号
        WechatAccount::where('name', '默认公众号')
            ->where('description', '从旧配置迁移的默认公众号')
            ->delete();
    }
};
