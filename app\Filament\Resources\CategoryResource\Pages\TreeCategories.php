<?php

namespace App\Filament\Resources\CategoryResource\Pages;

use App\Filament\Resources\CategoryResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use SolutionForest\FilamentTree\Components\Tree;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\BadgeColumn;
use Illuminate\Database\Eloquent\Builder;

class TreeCategories extends ListRecords
{
    protected static string $resource = CategoryResource::class;

    public function tree(): Tree
    {
        return Tree::make($this)
            ->primaryColumn('name')
            ->parentColumn('parent_id')
            ->enableSorting()
            ->sortColumn('sort_order')
            ->enableDragAndDrop()
            ->query(function (Builder $query) {
                return $query->with(['parent', 'children'])
                    ->orderBy('sort_order')
                    ->orderBy('name');
            })
            ->columns([
                TextColumn::make('name')
                    ->label('栏目名称')
                    ->searchable()
                    ->limit(50)
                    ->tooltip(fn ($record) => $record->getFullPath()),
                TextColumn::make('slug')
                    ->label('别名')
                    ->searchable()
                    ->copyable()
                    ->copyMessage('别名已复制')
                    ->limit(30),
                TextColumn::make('parent.name')
                    ->label('父级栏目')
                    ->default('顶级栏目')
                    ->limit(30),
                BadgeColumn::make('children_count')
                    ->label('子栏目数')
                    ->counts('children')
                    ->color('success'),
                BadgeColumn::make('posts_count')
                    ->label('文章数')
                    ->counts('posts')
                    ->color('primary'),
                TextColumn::make('sort_order')
                    ->label('排序')
                    ->sortable()
                    ->alignCenter(),
                IconColumn::make('is_active')
                    ->label('状态')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),
                TextColumn::make('created_at')
                    ->label('创建时间')
                    ->dateTime('Y-m-d H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ]);
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('table_view')
                ->label('表格视图')
                ->icon('heroicon-o-table-cells')
                ->color('info')
                ->url(static::getResource()::getUrl('index'))
                ->tooltip('切换到表格视图管理'),
            Actions\CreateAction::make()
                ->label('新增栏目'),
        ];
    }

    public function getTitle(): string
    {
        return '栏目树形管理';
    }

    public function getHeading(): string
    {
        return '栏目树形管理';
    }

    public function getSubheading(): ?string
    {
        return '通过树形结构管理栏目的层级关系，支持拖拽排序';
    }
}
