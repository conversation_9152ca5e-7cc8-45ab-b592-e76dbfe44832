<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('wechat_articles', function (Blueprint $table) {
            $table->id();
            $table->foreignId('wechat_account_id')->nullable()->constrained()->onDelete('set null')->comment('关联的微信公众号');
            $table->string('media_id')->nullable()->comment('微信素材ID');
            $table->string('title')->comment('文章标题');
            $table->text('digest')->nullable()->comment('文章摘要');
            $table->text('content')->comment('文章内容');
            $table->string('content_source_url')->nullable()->comment('原文链接');
            $table->string('url')->nullable()->comment('微信文章链接');
            $table->string('thumb_media_id')->nullable()->comment('封面图片素材ID');
            $table->string('thumb_url')->nullable()->comment('封面图片链接');
            $table->string('local_thumb_path')->nullable()->comment('本地封面图片路径');
            $table->string('author')->nullable()->comment('作者');
            $table->boolean('show_cover_pic')->default(true)->comment('是否显示封面');
            $table->integer('read_count')->default(0)->comment('阅读数');
            $table->integer('like_count')->default(0)->comment('点赞数');
            $table->integer('comment_count')->default(0)->comment('评论数');
            $table->string('collect_type')->default('api')->comment('采集方式：api接口，crawler爬虫');
            $table->string('collect_source')->nullable()->comment('采集来源');
            $table->timestamp('published_at')->nullable()->comment('发布时间');
            $table->timestamp('collected_at')->nullable()->comment('采集时间');
            $table->boolean('is_processed')->default(false)->comment('是否已处理');
            $table->boolean('is_published')->default(false)->comment('是否已发布到本站');
            $table->foreignId('article_id')->nullable()->constrained()->onDelete('set null')->comment('关联的本站文章ID');
            $table->json('meta_data')->nullable()->comment('元数据');
            $table->string('status')->default('pending')->comment('状态：pending待处理，processed已处理，published已发布，failed失败');
            $table->text('error_message')->nullable()->comment('错误信息');
            $table->timestamps();
            $table->softDeletes();
            
            $table->unique(['wechat_account_id', 'media_id']);
            $table->index(['status', 'is_processed']);
            $table->index(['collect_type', 'collected_at']);
            $table->index('published_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('wechat_articles');
    }
};