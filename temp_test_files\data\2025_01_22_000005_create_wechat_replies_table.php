<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('wechat_replies', function (Blueprint $table) {
            $table->id();
            $table->foreignId('wechat_account_id')->constrained()->onDelete('cascade');
            $table->string('name', 64)->comment('回复规则名称');
            $table->enum('type', ['subscribe', 'default', 'keyword', 'event'])->comment('回复类型');
            $table->enum('trigger_type', ['subscribe', 'keyword', 'default', 'click', 'scan'])->comment('触发类型');
            $table->json('keywords')->nullable()->comment('关键词列表');
            $table->enum('match_type', ['exact', 'partial', 'regex'])->default('exact')->comment('匹配类型');
            $table->enum('reply_type', ['text', 'image', 'voice', 'video', 'music', 'news'])->comment('回复内容类型');
            $table->text('reply_content')->nullable()->comment('回复内容');
            $table->string('reply_media_id', 128)->nullable()->comment('回复媒体ID');
            $table->string('reply_url', 512)->nullable()->comment('回复链接');
            $table->string('reply_title', 256)->nullable()->comment('回复标题');
            $table->text('reply_description')->nullable()->comment('回复描述');
            $table->string('reply_thumb_media_id', 128)->nullable()->comment('回复缩略图媒体ID');
            $table->boolean('is_active')->default(true)->comment('是否启用');
            $table->integer('priority')->default(0)->comment('优先级');
            $table->integer('usage_count')->default(0)->comment('使用次数');
            $table->timestamp('last_used_at')->nullable()->comment('最后使用时间');
            $table->timestamps();
            $table->softDeletes();
            
            $table->index(['wechat_account_id', 'type']);
            $table->index(['wechat_account_id', 'is_active']);
            $table->index(['wechat_account_id', 'trigger_type']);
            $table->index('priority');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('wechat_replies');
    }
};