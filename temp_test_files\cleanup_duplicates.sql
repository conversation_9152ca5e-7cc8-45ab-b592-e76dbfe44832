-- 清理行政区划表中的重复数据
-- 保留每个代码的最早记录（最小ID），删除其他重复记录

-- 1. 清理省级重复数据
DELETE t1 FROM administrative_institutions t1
INNER JOIN administrative_institutions t2 
WHERE 
    t1.administrative_level = 'province' 
    AND t2.administrative_level = 'province'
    AND t1.province_code = t2.province_code
    AND t1.id > t2.id;

-- 2. 清理市级重复数据
DELETE t1 FROM administrative_institutions t1
INNER JOIN administrative_institutions t2 
WHERE 
    t1.administrative_level = 'city' 
    AND t2.administrative_level = 'city'
    AND t1.city_code = t2.city_code
    AND t1.province_code = t2.province_code
    AND t1.id > t2.id;

-- 3. 清理区县级重复数据
DELETE t1 FROM administrative_institutions t1
INNER JOIN administrative_institutions t2 
WHERE 
    t1.administrative_level = 'district' 
    AND t2.administrative_level = 'district'
    AND t1.district_code = t2.district_code
    AND t1.city_code = t2.city_code
    AND t1.province_code = t2.province_code
    AND t1.id > t2.id;

-- 4. 清理乡镇级重复数据
DELETE t1 FROM administrative_institutions t1
INNER JOIN administrative_institutions t2 
WHERE 
    t1.administrative_level = 'town' 
    AND t2.administrative_level = 'town'
    AND t1.town_code = t2.town_code
    AND t1.district_code = t2.district_code
    AND t1.city_code = t2.city_code
    AND t1.province_code = t2.province_code
    AND t1.id > t2.id;

-- 查询清理后的统计信息
SELECT 
    administrative_level,
    COUNT(*) as total_count
FROM administrative_institutions 
GROUP BY administrative_level
ORDER BY 
    CASE administrative_level 
        WHEN 'province' THEN 1
        WHEN 'city' THEN 2
        WHEN 'district' THEN 3
        WHEN 'town' THEN 4
        ELSE 5
    END;