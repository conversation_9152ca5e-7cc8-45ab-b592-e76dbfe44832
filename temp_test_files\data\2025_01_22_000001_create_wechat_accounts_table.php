<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('wechat_accounts', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('公众号名称');
            $table->string('app_id')->unique()->comment('微信AppID');
            $table->string('app_secret')->comment('微信AppSecret');
            $table->string('token')->nullable()->comment('微信Token');
            $table->string('aes_key')->nullable()->comment('微信EncodingAESKey');
            $table->string('account_type')->default('subscription')->comment('账号类型：subscription订阅号，service服务号');
            $table->text('description')->nullable()->comment('公众号描述');
            $table->string('avatar')->nullable()->comment('公众号头像');
            $table->string('qr_code')->nullable()->comment('公众号二维码');
            $table->boolean('is_active')->default(true)->comment('是否启用');
            $table->boolean('auto_collect')->default(false)->comment('是否自动采集');
            $table->integer('collect_interval')->default(60)->comment('采集间隔(分钟)');
            $table->timestamp('last_collect_at')->nullable()->comment('最后采集时间');
            $table->json('collect_config')->nullable()->comment('采集配置');
            $table->timestamps();
            
            $table->index(['is_active', 'auto_collect']);
            $table->index('last_collect_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('wechat_accounts');
    }
};