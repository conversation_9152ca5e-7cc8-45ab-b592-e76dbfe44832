<?php

namespace App\Filament\Resources\CategoryResource\Pages;

use App\Filament\Resources\CategoryResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListCategories extends ListRecords
{
    protected static string $resource = CategoryResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('tree_view')
                ->label('树形视图')
                ->icon('heroicon-o-squares-2x2')
                ->color('info')
                ->url(static::getResource()::getUrl('tree'))
                ->tooltip('切换到树形视图管理'),
            Actions\CreateAction::make()
                ->label('新增栏目'),
        ];
    }

    public function getTitle(): string
    {
        return '栏目表格管理';
    }

    public function getHeading(): string
    {
        return '栏目表格管理';
    }

    public function getSubheading(): ?string
    {
        return '通过表格形式管理栏目，支持筛选和搜索';
    }
}