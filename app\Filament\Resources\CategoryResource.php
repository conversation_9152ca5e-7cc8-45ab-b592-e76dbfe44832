<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CategoryResource\Pages;
use App\Models\Category;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\Str;
use CodeWithDennis\FilamentSelectTree\SelectTree;
use SolutionForest\FilamentTree\Components\Tree;
class CategoryResource extends Resource
{

    protected static ?string $model = Category::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $navigationGroup = '内容管理';

    protected static ?int $navigationSort = 1;

    protected static ?int $navigationGroupSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->label('名称')
                    ->required()
                    ->maxLength(255)
                    ->live(onBlur: true)
                    ->afterStateUpdated(fn (string $operation, $state, Forms\Set $set) =>
                        $operation === 'create' ? $set('slug', Str::slug($state)) : null
                    ),
                Forms\Components\TextInput::make('slug')
                    ->label('别名')
                    ->required()
                    ->maxLength(255)
                    ->unique(ignoreRecord: true),
                SelectTree::make('parent_id')
                    ->label('父级分类')
                    ->relationship('parent', 'name', 'parent_id')
                    ->searchable(),
                Forms\Components\Textarea::make('description')
                    ->label('描述')
                    ->maxLength(65535)
                    ->columnSpanFull(),
                Forms\Components\Toggle::make('is_active')
                    ->label('是否启用')
                    ->default(true),
                Forms\Components\TextInput::make('sort_order')
                    ->label('排序')
                    ->numeric()
                    ->default(0),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('栏目名称')
                    ->searchable()
                    ->sortable()
                    ->formatStateUsing(function ($state, $record) {
                        // 递归查找父级，生成缩进
                        $level = 0;
                        $parent = $record->parent;
                        while ($parent) {
                            $level++;
                            $parent = $parent->parent;
                        }
                        $indent = str_repeat('├─ ', $level);
                        return $indent . $state;
                    })
                    ->html()
                    ->tooltip(fn ($record) => $record->getFullPath()),
                Tables\Columns\TextColumn::make('slug')
                    ->label('别名')
                    ->searchable()
                    ->copyable()
                    ->copyMessage('别名已复制'),
                Tables\Columns\TextColumn::make('parent.name')
                    ->label('父级栏目')
                    ->default('顶级栏目')
                    ->limit(30),
                Tables\Columns\BadgeColumn::make('children_count')
                    ->label('子栏目数')
                    ->counts('children')
                    ->color('success'),
                Tables\Columns\BadgeColumn::make('posts_count')
                    ->label('文章数')
                    ->counts('posts')
                    ->color('primary'),
                Tables\Columns\TextColumn::make('sort_order')
                    ->label('排序')
                    ->sortable()
                    ->alignCenter(),
                Tables\Columns\IconColumn::make('is_active')
                    ->label('状态')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('创建时间')
                    ->dateTime('Y-m-d H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('parent_id')
                    ->label('父级栏目')
                    ->relationship('parent', 'name')
                    ->placeholder('全部栏目'),
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('状态')
                    ->placeholder('全部状态')
                    ->trueLabel('已启用')
                    ->falseLabel('已禁用'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->before(function ($record) {
                        // 检查是否有子栏目或文章
                        if ($record->children()->count() > 0) {
                            throw new \Exception('该栏目下还有子栏目，无法删除');
                        }
                        if ($record->posts()->count() > 0) {
                            throw new \Exception('该栏目下还有文章，无法删除');
                        }
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('sort_order', 'asc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCategories::route('/'),
            'tree' => Pages\TreeCategories::route('/tree'),
            'create' => Pages\CreateCategory::route('/create'),
            'edit' => Pages\EditCategory::route('/{record}/edit'),
        ];
    }

    public static function getNavigationLabel(): string
    {
        return '栏目管理';
    }

    public static function getModelLabel(): string
    {
        return '栏目';
    }

    public static function getPluralModelLabel(): string
    {
        return '栏目';
    }
}