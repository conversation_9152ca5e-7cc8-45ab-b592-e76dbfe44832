<?php if (isset($component)) { $__componentOriginalb525200bfa976483b4eaa0b7685c6e24 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb525200bfa976483b4eaa0b7685c6e24 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament-widgets::components.widget','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament-widgets::widget'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <?php if (isset($component)) { $__componentOriginalee08b1367eba38734199cf7829b1d1e9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalee08b1367eba38734199cf7829b1d1e9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.section.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::section'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
         <?php $__env->slot('heading', null, []); ?> 
            备份详情
         <?php $__env->endSlot(); ?>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- 基本信息 -->
            <div class="space-y-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">基本信息</h3>

                <div class="space-y-3">
                    <div class="grid grid-cols-1 gap-1">
                        <span class="text-sm font-medium text-gray-500 dark:text-gray-400">备份名称:</span>
                        <span class="text-sm text-gray-900 dark:text-gray-100 break-all"><?php echo e($backup->name); ?></span>
                    </div>

                    <div class="grid grid-cols-1 gap-1">
                        <span class="text-sm font-medium text-gray-500 dark:text-gray-400">备份类型:</span>
                        <div>
                            <?php if (isset($component)) { $__componentOriginal986dce9114ddce94a270ab00ce6c273d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal986dce9114ddce94a270ab00ce6c273d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.badge','data' => ['color' => $backup->backup_type_color]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::badge'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['color' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($backup->backup_type_color)]); ?>
                                <?php echo e(\App\Models\DatabaseBackup::getBackupTypes()[$backup->backup_type] ?? $backup->backup_type); ?>

                             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal986dce9114ddce94a270ab00ce6c273d)): ?>
<?php $attributes = $__attributesOriginal986dce9114ddce94a270ab00ce6c273d; ?>
<?php unset($__attributesOriginal986dce9114ddce94a270ab00ce6c273d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal986dce9114ddce94a270ab00ce6c273d)): ?>
<?php $component = $__componentOriginal986dce9114ddce94a270ab00ce6c273d; ?>
<?php unset($__componentOriginal986dce9114ddce94a270ab00ce6c273d); ?>
<?php endif; ?>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 gap-1">
                        <span class="text-sm font-medium text-gray-500 dark:text-gray-400">状态:</span>
                        <div>
                            <?php if (isset($component)) { $__componentOriginal986dce9114ddce94a270ab00ce6c273d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal986dce9114ddce94a270ab00ce6c273d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.badge','data' => ['color' => $backup->status_color]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::badge'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['color' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($backup->status_color)]); ?>
                                <?php echo e(\App\Models\DatabaseBackup::getStatuses()[$backup->status] ?? $backup->status); ?>

                             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal986dce9114ddce94a270ab00ce6c273d)): ?>
<?php $attributes = $__attributesOriginal986dce9114ddce94a270ab00ce6c273d; ?>
<?php unset($__attributesOriginal986dce9114ddce94a270ab00ce6c273d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal986dce9114ddce94a270ab00ce6c273d)): ?>
<?php $component = $__componentOriginal986dce9114ddce94a270ab00ce6c273d; ?>
<?php unset($__componentOriginal986dce9114ddce94a270ab00ce6c273d); ?>
<?php endif; ?>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 gap-1">
                        <span class="text-sm font-medium text-gray-500 dark:text-gray-400">创建者:</span>
                        <span class="text-sm text-gray-900 dark:text-gray-100">
                            <?php echo e($backup->creator?->name ?? '系统'); ?>

                        </span>
                    </div>

                    <!--[if BLOCK]><![endif]--><?php if($backup->description): ?>
                    <div class="grid grid-cols-1 gap-1">
                        <span class="text-sm font-medium text-gray-500 dark:text-gray-400">描述:</span>
                        <span class="text-sm text-gray-900 dark:text-gray-100 break-words"><?php echo e($backup->description); ?></span>
                    </div>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </div>
            </div>

            <!-- 文件信息 -->
            <div class="space-y-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">文件信息</h3>

                <div class="space-y-3">
                    <!--[if BLOCK]><![endif]--><?php if($backup->file_path): ?>
                    <div class="grid grid-cols-1 gap-1">
                        <span class="text-sm font-medium text-gray-500 dark:text-gray-400">文件路径:</span>
                        <span class="text-sm text-gray-900 dark:text-gray-100 font-mono break-all bg-gray-50 dark:bg-gray-800 p-2 rounded"><?php echo e($backup->file_path); ?></span>
                    </div>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                    <!--[if BLOCK]><![endif]--><?php if($backup->file_size): ?>
                    <div class="grid grid-cols-1 gap-1">
                        <span class="text-sm font-medium text-gray-500 dark:text-gray-400">文件大小:</span>
                        <span class="text-sm text-gray-900 dark:text-gray-100"><?php echo e($backup->formatted_file_size); ?></span>
                    </div>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                    <div class="grid grid-cols-1 gap-1">
                        <span class="text-sm font-medium text-gray-500 dark:text-gray-400">文件存在:</span>
                        <div>
                            <!--[if BLOCK]><![endif]--><?php if($backup->fileExists()): ?>
                                <?php if (isset($component)) { $__componentOriginal986dce9114ddce94a270ab00ce6c273d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal986dce9114ddce94a270ab00ce6c273d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.badge','data' => ['color' => 'success']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::badge'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['color' => 'success']); ?>是 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal986dce9114ddce94a270ab00ce6c273d)): ?>
<?php $attributes = $__attributesOriginal986dce9114ddce94a270ab00ce6c273d; ?>
<?php unset($__attributesOriginal986dce9114ddce94a270ab00ce6c273d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal986dce9114ddce94a270ab00ce6c273d)): ?>
<?php $component = $__componentOriginal986dce9114ddce94a270ab00ce6c273d; ?>
<?php unset($__componentOriginal986dce9114ddce94a270ab00ce6c273d); ?>
<?php endif; ?>
                            <?php else: ?>
                                <?php if (isset($component)) { $__componentOriginal986dce9114ddce94a270ab00ce6c273d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal986dce9114ddce94a270ab00ce6c273d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.badge','data' => ['color' => 'danger']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::badge'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['color' => 'danger']); ?>否 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal986dce9114ddce94a270ab00ce6c273d)): ?>
<?php $attributes = $__attributesOriginal986dce9114ddce94a270ab00ce6c273d; ?>
<?php unset($__attributesOriginal986dce9114ddce94a270ab00ce6c273d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal986dce9114ddce94a270ab00ce6c273d)): ?>
<?php $component = $__componentOriginal986dce9114ddce94a270ab00ce6c273d; ?>
<?php unset($__componentOriginal986dce9114ddce94a270ab00ce6c273d); ?>
<?php endif; ?>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 时间信息 -->
            <div class="space-y-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">时间信息</h3>

                <div class="space-y-3">
                    <div class="grid grid-cols-1 gap-1">
                        <span class="text-sm font-medium text-gray-500 dark:text-gray-400">创建时间:</span>
                        <span class="text-sm text-gray-900 dark:text-gray-100">
                            <?php echo e($backup->created_at?->format('Y-m-d H:i:s')); ?>

                        </span>
                    </div>

                    <!--[if BLOCK]><![endif]--><?php if($backup->backup_started_at): ?>
                    <div class="grid grid-cols-1 gap-1">
                        <span class="text-sm font-medium text-gray-500 dark:text-gray-400">开始时间:</span>
                        <span class="text-sm text-gray-900 dark:text-gray-100">
                            <?php echo e($backup->backup_started_at->format('Y-m-d H:i:s')); ?>

                        </span>
                    </div>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                    <!--[if BLOCK]><![endif]--><?php if($backup->backup_completed_at): ?>
                    <div class="grid grid-cols-1 gap-1">
                        <span class="text-sm font-medium text-gray-500 dark:text-gray-400">完成时间:</span>
                        <span class="text-sm text-gray-900 dark:text-gray-100">
                            <?php echo e($backup->backup_completed_at->format('Y-m-d H:i:s')); ?>

                        </span>
                    </div>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                    <!--[if BLOCK]><![endif]--><?php if($backup->duration): ?>
                    <div class="grid grid-cols-1 gap-1">
                        <span class="text-sm font-medium text-gray-500 dark:text-gray-400">耗时:</span>
                        <span class="text-sm text-gray-900 dark:text-gray-100"><?php echo e($backup->duration); ?></span>
                    </div>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </div>
            </div>

        </div>

        <!-- 错误信息 -->
        <!--[if BLOCK]><![endif]--><?php if($backup->error_message): ?>
        <div class="mt-6 space-y-4">
            <h3 class="text-lg font-medium text-red-600 dark:text-red-400">错误信息</h3>

            <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                <p class="text-sm text-red-700 dark:text-red-300 font-mono break-words whitespace-pre-wrap"><?php echo e($backup->error_message); ?></p>
            </div>
        </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

        <!-- 备份信息 -->
        <!--[if BLOCK]><![endif]--><?php if($backup->schedule): ?>
        <div class="mt-6 space-y-4">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">备份信息</h3>

            <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                <div class="grid grid-cols-1 gap-2">
                    <div class="grid grid-cols-1 gap-1">
                        <span class="text-sm font-medium text-blue-700 dark:text-blue-300">备份计划:</span>
                        <span class="text-sm text-blue-900 dark:text-blue-100"><?php echo e($backup->schedule->name); ?></span>
                    </div>

                    <div class="grid grid-cols-1 gap-1">
                        <span class="text-sm font-medium text-blue-700 dark:text-blue-300">计划描述:</span>
                        <span class="text-sm text-blue-900 dark:text-blue-100"><?php echo e($backup->schedule->description ?? '无'); ?></span>
                    </div>

                    <div class="grid grid-cols-1 gap-1">
                        <span class="text-sm font-medium text-blue-700 dark:text-blue-300">执行频率:</span>
                        <span class="text-sm text-blue-900 dark:text-blue-100"><?php echo e($backup->schedule->frequency_description); ?></span>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $attributes = $__attributesOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $component = $__componentOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__componentOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb525200bfa976483b4eaa0b7685c6e24)): ?>
<?php $attributes = $__attributesOriginalb525200bfa976483b4eaa0b7685c6e24; ?>
<?php unset($__attributesOriginalb525200bfa976483b4eaa0b7685c6e24); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb525200bfa976483b4eaa0b7685c6e24)): ?>
<?php $component = $__componentOriginalb525200bfa976483b4eaa0b7685c6e24; ?>
<?php unset($__componentOriginalb525200bfa976483b4eaa0b7685c6e24); ?>
<?php endif; ?>
<?php /**PATH C:\cms-admin\resources\views/filament/widgets/backup-details.blade.php ENDPATH**/ ?>